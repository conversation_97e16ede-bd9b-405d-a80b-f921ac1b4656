//
//  File.swift
//  MatchIQ
//
//  Created by <PERSON> on 4/25/25.
//

import Foundation
import Vapor
import Fluent

struct ClientProfileContext: Encodable {
    let title: String
    let client: Client
    let responses: [ClientResponse]
    let user: User?
    let isStaff: Bool
}

struct CaregiverProfileContext: Encodable {
    let title: String
    let caregiver: Caregiver
    let responses: [CaregiverResponse]
    let user: User?
    let isStaff: Bool
}

struct CaregiverMatchesContext: Encodable {
    let title: String
    let caregiver: Caregiver
    let matches: [ClientMatchSummary]
}

struct CaregiverSingleMatchContext: Encodable {
    let title: String
    let caregiver: Caregiver
    let match: ClientMatchViewModel
    let client: Client
    let clientResponses: [ClientResponse]
    let currentIndex: Int
    let totalMatches: Int
    let hasNext: Bool
    let hasPrevious: Bool
    let confirmationStatus: String?
}

struct ClientMatchViewModel: Encodable {
    let clientId: UUID
    let clientName: String
    let matchPercentage: Int
    let categoryScores: MatchCategoryScores
}


struct MatchCategoryScores: Encodable {
    let personalityCommunication: Int
    let careNeedsSkills: Int
    let lifestyleInterests: Int
    let culturalLanguageLocation: Int
    let logisticsSchedule: Int
}

struct MatchViewModel: Encodable {
    let caregiverId: UUID
    let caregiverName: String
    let matchPercentage: Int
    let categoryScores: MatchCategoryScores
}

struct ClientMatchesContext: Encodable {
    let title: String
    let client: Client
    let matches: [MatchViewModel]
}

struct SingleMatchContext: Encodable {
    let title: String
    let client: Client
    let match: MatchViewModel
    let caregiver: Caregiver
    let caregiverResponses: [CaregiverResponse]
    let currentIndex: Int
    let totalMatches: Int
    let hasNext: Bool
    let hasPrevious: Bool
    let confirmationStatus: String?
}

struct MatchConfirmationRequest: Content {
    let status: String
    let notes: String?
}

struct ClientWebController: RouteCollection {
    func boot(routes: any RoutesBuilder) throws {
        // GET /client/register
        routes.get("register", use: registerHandler)

        // GET /client/:clientID
        routes.get(":clientID", use: showHandler)

        // Match routes - protected by MatchAccessGuard (staff only)
        let matchRoutes = routes.grouped(MatchAccessGuard())

        // GET /client/:clientID/matches
        matchRoutes.get(":clientID", "matches", use: matchesHandler)

        // GET /client/:clientID/matches/empty - Show empty matches state
        matchRoutes.get(":clientID", "matches", "empty", use: emptyMatchesHandler)

        // GET /client/:clientID/matches/:index - View single match by index
        matchRoutes.get(":clientID", "matches", ":index", use: singleMatchHandler)

        // POST /client/:clientID/matches/:caregiverID/confirm - Confirm/reject a match
        matchRoutes.post(":clientID", "matches", ":caregiverID", "confirm", use: confirmMatchHandler)
    }

    struct QuestionViewResponse: Encodable {
        let title: String
        let questions: [QuestionView]
        let user: User?
        let isStaff: Bool
    }

    struct QuestionView: Encodable {
        let text: String
        let options: [String]
        let index: Int
    }

    // GET /client/register
    func registerHandler(_ req: Request) async throws -> View {
        //        return try await req.view.render("client/register", ["title": "Client Registration"])
        let questions = try await Question.query(on: req.db).all()

        let questionDTOs = questions.enumerated().map { (index, question) in
            QuestionView(text: question.questionText, options: question.options, index: index)
        }

        let data = QuestionViewResponse(
            title: "Client Registration",
            questions: questionDTOs,
            user: req.user,
            isStaff: req.user?.role == .staff
        )

        return try await req.view.render("client/register", data)
    }

    // GET /client/:clientID
    func showHandler(_ req: Request) async throws -> View {
        guard let clientIDString = req.parameters.get("clientID"),
              let clientID = UUID(clientIDString) else {
            throw Abort(.badRequest, reason: "Invalid client ID")
        }

        guard let client = try await Client.find(clientID, on: req.db) else {
            throw Abort(.notFound, reason: "Client not found")
        }

        let responses = try await ClientResponse.query(on: req.db)
            .filter(\.$client.$id == clientID)
            .all()

        let context = ClientProfileContext(
            title: "Client Profile",
            client: client,
            responses: responses,
            user: req.user,
            isStaff: req.user?.role == .staff
        )

        return try await req.view.render("client/profile", context)
    }

    // GET /client/:clientID/matches - Redirect to first match or show empty state
    func matchesHandler(_ req: Request) async throws -> Response {
        guard let clientIDString = req.parameters.get("clientID"),
              let clientID = UUID(clientIDString) else {
            throw Abort(.badRequest, reason: "Invalid client ID")
        }

        guard let client = try await Client.find(clientID, on: req.db) else {
            throw Abort(.notFound, reason: "Client not found")
        }

        let matches = try await MatchingService.findMatches(for: client, on: req.db)

        if matches.isEmpty {
            // If no matches, redirect to empty state view
            return req.redirect(to: "/client/\(clientID)/matches/empty")
        }

        // Redirect to first match
        return req.redirect(to: "/client/\(clientID)/matches/0")
    }

    // GET /client/:clientID/matches/empty - Show empty matches state
    func emptyMatchesHandler(_ req: Request) async throws -> View {
        guard let clientIDString = req.parameters.get("clientID"),
              let clientID = UUID(clientIDString) else {
            throw Abort(.badRequest, reason: "Invalid client ID")
        }

        guard let client = try await Client.find(clientID, on: req.db) else {
            throw Abort(.notFound, reason: "Client not found")
        }

        let context = ClientMatchesContext(title: "Caregiver Matches", client: client, matches: [])
        return try await req.view.render("client/matches-empty", context)
    }

    // GET /client/:clientID/matches/:index - View single match by index
    func singleMatchHandler(_ req: Request) async throws -> View {
        guard let clientIDString = req.parameters.get("clientID"),
              let clientID = UUID(clientIDString),
              let indexString = req.parameters.get("index"),
              let index = Int(indexString) else {
            throw Abort(.badRequest, reason: "Invalid parameters")
        }

        guard let client = try await Client.find(clientID, on: req.db) else {
            throw Abort(.notFound, reason: "Client not found")
        }

        let matches = try await MatchingService.findMatches(for: client, on: req.db)

        guard index >= 0 && index < matches.count else {
            throw Abort(.badRequest, reason: "Invalid match index")
        }

        let match = matches[index]

        // Get caregiver details
        guard let caregiver = try await Caregiver.find(match.caregiverId, on: req.db) else {
            throw Abort(.notFound, reason: "Caregiver not found")
        }

        // Get caregiver responses
        let caregiverResponses = try await CaregiverResponse.query(on: req.db)
            .filter(\.$caregiver.$id == match.caregiverId)
            .all()

        // Check if there's an existing confirmation
        let existingConfirmation = try await MatchConfirmation.query(on: req.db)
            .filter(\.$client.$id == clientID)
            .filter(\.$caregiver.$id == match.caregiverId)
            .first()

        let formattedMatch = MatchViewModel(
            caregiverId: match.caregiverId,
            caregiverName: match.caregiverName,
            matchPercentage: Int(match.matchPercentage),
            categoryScores: MatchCategoryScores(
                personalityCommunication: Int(match.categoryScores["personalityCommunication"] ?? 0),
                careNeedsSkills: Int(match.categoryScores["careNeedsSkills"] ?? 0),
                lifestyleInterests: Int(match.categoryScores["lifestyleInterests"] ?? 0),
                culturalLanguageLocation: Int(match.categoryScores["culturalLanguageLocation"] ?? 0),
                logisticsSchedule: Int(match.categoryScores["logisticsSchedule"] ?? 0)
            )
        )

        let context = SingleMatchContext(
            title: "Caregiver Match",
            client: client,
            match: formattedMatch,
            caregiver: caregiver,
            caregiverResponses: caregiverResponses,
            currentIndex: index,
            totalMatches: matches.count,
            hasNext: index < matches.count - 1,
            hasPrevious: index > 0,
            confirmationStatus: existingConfirmation?.status.rawValue
        )

        return try await req.view.render("client/single-match", context)
    }

    // POST /client/:clientID/matches/:caregiverID/confirm - Confirm/reject a match
    func confirmMatchHandler(_ req: Request) async throws -> Response {
        // Only staff can access this functionality due to MatchAccessGuard
        let user = try req.requireStaff()

        guard let clientIDString = req.parameters.get("clientID"),
              let clientID = UUID(clientIDString),
              let caregiverIDString = req.parameters.get("caregiverID"),
              let caregiverID = UUID(caregiverIDString) else {
            throw Abort(.badRequest, reason: "Invalid parameters")
        }

        let confirmationRequest = try req.content.decode(MatchConfirmationRequest.self)

        guard let status = ConfirmationStatus(rawValue: confirmationRequest.status) else {
            throw Abort(.badRequest, reason: "Invalid status")
        }

        // Find the match result
        guard let matchResult = try await MatchResult.query(on: req.db)
            .filter(\.$client.$id == clientID)
            .filter(\.$caregiver.$id == caregiverID)
            .first() else {
            throw Abort(.notFound, reason: "Match not found")
        }

        // Check if confirmation already exists
        if let existingConfirmation = try await MatchConfirmation.query(on: req.db)
            .filter(\.$client.$id == clientID)
            .filter(\.$caregiver.$id == caregiverID)
            .first() {
            // Update existing confirmation
            existingConfirmation.status = status
            existingConfirmation.notes = confirmationRequest.notes
            try await existingConfirmation.save(on: req.db)
        } else {
            // Create new confirmation
            let confirmation = MatchConfirmation(
                clientID: clientID,
                caregiverID: caregiverID,
                matchResultID: matchResult.id!,
                status: status,
                notes: confirmationRequest.notes
            )
            try await confirmation.save(on: req.db)
        }

        // Log the action
        req.logger.info("Staff user \(user.email) \(status.rawValue) match between client \(clientID) and caregiver \(caregiverID)")

        // For rejections, implement the improved flow to show next match
        if status == .rejected {
            // Get current match index to find the next one
            guard let client = try await Client.find(clientID, on: req.db) else {
                throw Abort(.notFound, reason: "Client not found")
            }

            let matches = try await MatchingService.findMatches(for: client, on: req.db)

            // Find current match index
            if let currentIndex = matches.firstIndex(where: { $0.caregiverId == caregiverID }) {
                let nextIndex = currentIndex + 1

                // If there's a next match, redirect to it
                if nextIndex < matches.count {
                    return req.redirect(to: "/client/\(clientID)/matches/\(nextIndex)")
                }
            }

            // No more matches, redirect to matches overview
            return req.redirect(to: "/staff/matches")
        }

        // For confirmations, redirect to staff dashboard
        return req.redirect(to: "/staff/matches")
    }
}
